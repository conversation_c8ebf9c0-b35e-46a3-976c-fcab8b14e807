applicationName: venus-config

replicaCount: 1

image:
  repository: "{{ .Values.global.imageHost }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.4-alpha"

# Filebeat 配置
filebeat:
  image:
    repository: "{{ .Values.global.imageHost }}/galaxy/filebeat"
    tag: ********
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 30m
      memory: 500Mi

service:
  port: 8888
  type: "ClusterIP"
  # ipAddress: localhost

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  - name: application-properties
    mountPath: /data/app/config/application.properties
    subPath: application.properties
  - name: application
    mountPath: /data/app/config/application.yml
    subPath: application.yml
  - name: bootstrap
    mountPath: /data/app/config/bootstrap.yml
    subPath: bootstrap.yml

autoscaling:
  enabled: false

ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
  hosts:
    - host: ""
      paths:
        - path: /
          pathType: Prefix

# Volumes 配置
volumes:
  - name: logfile
    emptyDir: {}

# Nacos 配置初始化
nacosExt:
  initImage:
    repository: curlimages/curl
    tag: latest

  # 要发布到 Nacos 的配置
  config:
    dataId: venusconfig.properties  # 配置的 dataId
    group: DEFAULT_GROUP                  # 配置的 group，默认为 DEFAULT_GROUP

  # 配置文件来源
  configFile:
    configMap: venusconfig-properties    # 包含配置内容的 ConfigMap 名称
    fileName: venusconfig.properties                  # ConfigMap 中的文件名
    mountPath: /data/app         # 挂载路径
