applicationName: venus-gateway

profile: "dev"

replicaCount: 1

image:
  repository: "{{ .Values.global.imageHost }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.3-alpha"

# Filebeat 配置
filebeat:
  image:
    repository: "{{ .Values.global.imageHost }}/galaxy/filebeat"
    tag: ********
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 30m
      memory: 500Mi

service:
  port: 7073
  type: "ClusterIP"
  # ipAddress: localhost

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  - name: application
    mountPath: /data/app/config/application.yml
    subPath: application.yml
  - name: bootstrap
    mountPath: /data/app/config/bootstrap.yml
    subPath: bootstrap.yml


autoscaling:
  enabled: false

ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
  hosts:
    - host: ""
      paths:
        - path: /
          pathType: Prefix

# Volumes 配置
volumes:
  - name: logfile
    emptyDir: {}

redis:
  timeout: 60s
  jedis:
    pool:
      maxIdle: 500
      minIdle: 50
      maxWait: -1s
      maxActive: -1

env:
  - name: config-service-host
    value: "{{ .Values.global.config.service.host }}"
  - name: com.dcfs.venus.route.islimits
    value: "false"
  - name: com.dcfs.venus.health
    value: "false"
  - name: com.dcfs.venus.app.scenecontrol.checkversion
    value: "true"
  - name: com.dcfs.venus.digitalsignature
    value: "{{ .Values.global.venus.digitalsignature }}"
  - name: com.dcfs.venus.playbackattack
    value: "true"
  - name: com.dcfs.venus.playbackattack.attackinterval
    value: "15000"
  - name: com.dcfs.venus.playbackattack.differenceinterval
    value: "30000"
  - name: com.dcfs.venus.security.model
    value: "sm"
  - name: com.dcfs.venus.app.mqenable
    value: "{{ .Values.global.venus.mq.enable }}"
  - name: com.dcfs.venus.app.mqtype
    value: "{{ .Values.global.venus.mq.type }}"
  - name: com.dcfs.venus.app.rabbitmq.host
    value: "localhost"
  - name: com.dcfs.venus.app.rabbitmq.port
    value: "5672"
  - name: com.dcfs.venus.app.rabbitmq.username
    value: "guest"
  - name: com.dcfs.venus.app.rabbitmq.password
    value: "guest"
  - name: com.dcfs.venus.app.redis.host
    value: "{{ .Values.global.middleware.defaultPlatform.redis.host }}"
  - name: com.dcfs.venus.app.redis.port
    value: "{{ .Values.global.middleware.defaultPlatform.redis.port }}"
  - name: com.dcfs.venus.app.redis.passwd
    value: "{{ .Values.global.middleware.defaultPlatform.redis.password }}"
  - name: com.dcfs.venus.databuffer.sharedstorage
    value: "/etc/databuffer/bufferdeploy-dev.xml"
  - name: com.dcfs.venus.scheduler.taskassign
    value: "/etc/scheduler/task-assign.xml"
  - name: com.dcfs.venus.scheduler.coordination
    value: "/etc/scheduler/coordination-dev.xml"
  - name: com.dcfs.venus.scheduler.sqlscript
    value: "/sql/mysql_sql/platform-scheduler.xml,/sql/mysql_sql/platform-datacenter.xml"
  - name: com.dcfs.venus.datastorage.pool
    value: "/etc/datasource/pool-dev.xml"
  - name: com.dcfs.venus.workflow.init
    value: "/etc/workflow/activiti.cfg.xml"
  - name: com.dcfs.venus.app.scene.flow
    value: "/scene-flows/loadindex/flowindex.xml"
  - name: com.dcfs.venus.route.file
    value: "/router/platform/router-platform.xml,/router/app/router-app.xml,/router/app/router-app-pmw.xml,/router/app/router-app-custom.xml,/router/app/router-app-depn.xml"
  - name: com.dcfs.venus.acquisition
    value: "/etc/acquisition/acquisition_param.properties"
  - name: com.dcfs.venus.zkcoordination
    value: "/etc/zk_coordination/coordination-dev.xml"
  - name: com.dcfs.venus.app.file
    value: "/etc/fileupload/fileupload.xml"
  - name: com.dcfs.venus.app.appparam
    value: "/etc/appparam/app-param.xml"
  - name: com.dcfs.venus.app.remote.adapter
    value: "/etc/adapter/channel-dev.xml"
  - name: com.dcfs.venus.app.security
    value: "/etc/security/outtersecurity-dev.xml,/etc/security/innersecurity-dev.xml"
  - name: com.dcfs.venus.app.security.encryptparam.encryptkey
    value: "teller"
  - name: com.dcfs.venus.app.libextend
    value: "/plugins/libextend/channel-extend.xml"
  - name: com.dcfs.venus.cms.config
    value: "/etc/cms/cmsconfig.xml"
  - name: com.dcfs.venus.datadictmg.sqlscript
    value: "/sql/mysql_sql/platform-dictmg.xml"
  - name: com.dcfs.venus.app.scenecontrol.bdkmodule
    value: "bdk"
  - name: com.dcfs.venus.app.scenecontrol.scenemodule
    value: "scene"
  - name: com.dcfs.venus.app.scenecontrol.sqlscript
    value: "/sql/mysql_sql/platform-scenecontrol.xml"
  - name: com.dcfs.venus.app.scenecontrol.security
    value: "/etc/security/innersecurity-dev.xml"
  - name: com.dcfs.venus.app.scenecontrol.dynamictemplate
    value: "/etc/viewtemplate"
  - name: com.dcfs.venus.app.param
    value: "/etc/appparam/app-param.xml,/etc/appparam/app-param-pmw.xml,/etc/appparam/infoairfm-app-param.xml"
  - name: com.dcfs.venus.app.spare.sqlscript
    value: "/sql/mysql_sql/platform-app-sparedata.xml,/sql/mysql_sql/platform_app_sparedata_depn.xml,/sql/mysql_sql/tellerStage_ensemble.xml,/sql/mysql_sql/tellerStage_extends.xml,/sql/mysql_sql/tellerStage_fx.xml,/sql/mysql_sql/tellerStage_pl.xml,/sql/mysql_sql/tellerStage_sso.xml,/sql/mysql_sql/tellerX_ensemble.xml"
  - name: com.dcfs.venus.app.spare.regscript
    value: "/sparedata/register/registerSpareData-dev.xml"
  - name: com.dcfs.venus.app.jointsidecar.service
    value: "/service/platform_app_pmw/remote-services.xml,/service/platform_app_monitor/remote-services.xml,/service/platform_app_voucher/remote-services.xml,/service/platform_app_auth/remote-services.xml"
  - name: com.dcfs.venus.app.jointsidecar.config
    value: "/etc/jointsidecar/sidecar-config.xml"
  - name: com.dcfs.venus.app.infoaffirm.serviceinterceptor
    value: "/plugins/interceptor/infoaffirm-service-interceptor.xml"
  - name: com.dcfs.venus.app.unifyprint.unifiedprintinterceptor
    value: "/plugins/interceptor/unifiedprint-service-interceptor.xml"
  - name: com.dcfs.venus.app.unifyprint.sqlscript
    value: "/sql/mysql_sql/app-unifiedprint.xml"
  - name: com.dcfs.venus.app.unifyprint.tranconfig
    value: "/plugins/unifiedprint/unifiedprint-config.xml"
  - name: com.dcfs.venus.app.user.sqlscript
    value: "/sql/mysql_sql/platform-app-user.xml,/sql/mysql_sql/platform-app-org.xml,/sql/mysql_sql/platform-app-post.xml,/sql/mysql_sql/platform-app-role.xml,/sql/mysql_sql/platform-app-amt.xml,/sql/mysql_sql/platform-app-theme.xml,/sql/mysql_sql/app-unifiedprint.xml"
  - name: com.dcfs.venus.app.user.service
    value: "/service/platform_app_user/local-services.xml,/service/platform_app_user/composite-services.xml"
  - name: com.dcfs.venus.app.custom.service
    value: "/service/custom/remote-services.xml"
  - name: com.dcfs.venus.app.custom.sqlscript
    value: "/sql/mysql_sql/platform-app-depn.xml,sql/mysql_sql/tellerStage_ensemble.xml,sql/mysql_sql/tellerStage_extends.xml,sql/mysql_sql/tellerStage_fx.xml,sql/mysql_sql/tellerStage_pl.xml,sql/mysql_sql/tellerStage_sso.xml"
  - name: com.dcfs.venus.app.depn.service
    value: "/service/platform_app_depn/remote-services.xml"
  - name: com.dcfs.venus.app.depn.sqlscript
    value: "/sql/mysql_sql/platform-app-depn.xml,sql/mysql_sql/tellerStage_ensemble.xml,sql/mysql_sql/tellerStage_extends.xml,sql/mysql_sql/tellerStage_fx.xml,sql/mysql_sql/tellerStage_pl.xml,sql/mysql_sql/tellerStage_sso.xml"
  - name: com.dcfs.venus.app.sysparam.service
    value: "/service/platform_app_sysparam/local-services.xml"
  - name: com.dcfs.venus.app.sysparam.sqlscript
    value: "/sql/mysql_sql/platform-sysparam.xml,/sql/mysql_sql/platform-dictmg.xml"
  - name: com.dcfs.venus.app.sysparam.cache
    value: "auth,check"
  - name: com.dcfs.venus.app.journal.sqlscript
    value: "JOURNAL:/sql/mysql_sql/platform-app-journal.xml"
  - name: com.dcfs.venus.app.authcheck
    value: "/sql/mysql_sql/app-auth-check.xml"
  - name: com.dcfs.venus.app.busincheck
    value: "/sql/mysql_sql/app-busincheck-check.xml"
  - name: com.dcfs.venus.app.busincheck.serviceinterceptor
    value: "/plugins/interceptor/busincheck-service-interceptor.xml"
  - name: com.dcfs.venus.app.auth.serviceinterceptor
    value: "/plugins/interceptor/auth-service-interceptor.xml"
  - name: com.dcfs.venus.app.auth.param
    value: "/etc/appparam/app-param.xml"
  - name: com.dcfs.venus.app.auth.sqlscript
    value: "/sql/mysql_sql/app-auth-apply.xml,/sql/mysql_sql/app-auth-dispose.xml,/sql/mysql_sql/app-busincheck.xml,/sql/mysql_sql/app-approval.xml"
  - name: com.dcfs.venus.app.auth.service
    value: "/service/platform_app_auth/local-services.xml"
  - name: com.dcfs.venus.app.nlp.neo4j.switch
    value: "false"
  - name: com.dcfs.venus.app.nlp.neo4j.ip
    value: "localhost:7687"
  - name: com.dcfs.venus.app.nlp.neo4j.username
    value: "neo4j"
  - name: com.dcfs.venus.app.nlp.neo4j.password
    value: "123456"
  - name: com.dcfs.venus.app.voucher.service
    value: "/service/platform_app_voucher/remote-services.xml"
  - name: com.dcfs.venus.taskcontrol.sqlscript
    value: "/sql/mysql_sql/platform-app-taskcontrol.xml,/sql/mysql_sql/app-im.xml"
  - name: com.dcfs.venus.app.im.service
    value: "/service/platform_app_im/local-services.xml"
  - name: com.dcfs.venus.app.im.loginconfig
    value: ""
  - name: com.dcfs.venus.app.synergy.flow
    value: ""
  - name: com.dcits.operationsm.pmw.sqlscript
    value: "PMW:/sql/pmw_mysql_sql/user.xml,PMW:/sql/pmw_mysql_sql/pmw.xml,PMW:/sql/pmw_mysql_sql/device.xml,PMW:/sql/pmw_mysql_sql/common.xml,PMW:/sql/pmw_mysql_sql/secretkey.xml"
  - name: com.dcits.operationsm.pmw.service
    value: "/service/pmw/local-services.xml,/service/pmw/composite-services.xml"
  - name: com.dcits.operationsm.pmw.mappingrule
    value: "/mapping/pmw/TELLER_DEVICE_C_MAPPING.xml"
  - name: com.dcits.operationsm.pmw.receipttemplate
    value: "/template/register/registerTemplate.xml"
  - name: com.dcits.operationsm.pmw.spare.regscript
    value: "/sparedata/register/registerSpareData-pmw.xml"
  - name: com.dcfs.venus.app.tellerinfoaffirm.param
    value: "/etc/appparam/infoairfm-app-param.xml"
  - name: com.dcfs.venus.app.tellerinfoaffirm.serviceinterceptor
    value: "/plugins/interceptor/teller-infoaffirm-service-interceptor.xml"
  - name: com.dcfs.venus.app.tellerinfoaffirm.sql
    value: "/sql/mysql_sql/app-teller-infoaffirm.xml"
  - name: com.dcfs.venus.app.tellerinfoaffirm.registerInfoaffirmTemplate
    value: "/template/register/registerInfoaffirmTemplate.xml"
  - name: com.dcits.operationsm.receipt.extend
    value: "/plugins/libextend/channel-extend.xml"
  - name: com.dcits.operationsm.receipt.mapping
    value: "/mapping/receipt/RECEIPT_DATA_COLLECTOR_MAPPING.xml"
  - name: com.dcits.operationsm.receipt.sqlscript
    value: "RECEIPT:/sql/receipt_oracle_sql/app-im.xml,RECEIPT:/sql/receipt_oracle_sql/OMSUserManage.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-authentication.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-journal.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-parameter.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-parameter-sparedata.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-personnel.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-process.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-receipt.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-receipt-manager-menu.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-receipt-manager-role.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-receipt-manager-user.xml,RECEIPT:/sql/receipt_oracle_sql/platform-app-receipt-trade.xml"
  - name: com.dcits.operationsm.receipt.service
    value: "/service/receipt/remote-services.xml"
  - name: com.dcits.operationsm.receipt.datarouter
    value: "/plugins/receipt/receipt-data-router.xml"
  - name: com.dcits.operationsm.receipt.threadpool
    value: "/plugins/receipt/receipt-data-threadpool.xml"
