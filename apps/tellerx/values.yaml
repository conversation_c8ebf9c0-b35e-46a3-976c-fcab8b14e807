global:
  namespace: "onebank"

  # podSecurityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000
  #   fsGroup: 1000

  # securityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000

  # Default image host
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"

  # Default image pull policy
  imagePullPolicy: "IfNotPresent"

  initImage:
    repository: "{{ .Values.global.imageHost }}/onebank/busybox"
    tag: "latest"
    pullPolicy: "IfNotPresent"

  config:
    service:
      host: "http://tellerx-venus-core-configservice.onebank:8888/"

  middleware:
    defaultPlatform:
      nacos:
        # serverAddr: "nacosx-headless.paas.svc.cluster.local:8848"
        serverAddr: "tellerx-nacos-server.paas:8848"
        username: nacos
        password: nacos
      kafka:
        service: kafka.galaxy:9092
      redis:
        database: 0
        host: "redis-standalone-redis.paas"
        port: 6379
        password: ""
        lettuce:
          pool:
            maxActive: 20
            maxWait: 3000
            maxIdle: 5
            minIdle: 0
          timeout: 15000

  venus:
    digitalsignature: true
    mq:
      enable: false
      type: rabbitmq
      producer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"
      consumer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"

  eureka:
    client:
      enabled: false
      registerWithEureka: true
      fetchRegistry: true
      serviceUrl:
        defaultZone: "http://localhost:7071/eureka/"
      healthcheck:
        enabled: false

venus-app-auth:
  enabled: true
venus-app-custom:
  enabled: true
venus-app-depn:
  enabled: true
venus-app-pmw:
  enabled: true
venus-app-sysparam:
  enabled: true
venus-app-user:
  enabled: true
venus-core-configservice:
  enabled: true
venus-core-environment:
  enabled: true
venus-core-gateway:
  enabled: true
venus-core-journal:
  enabled: true
venus-core-scenecontrol:
  enabled: true
venus-core-taskcontrol:
  enabled: true
openfire:
  enabled: true
simulator:
  enabled: true
